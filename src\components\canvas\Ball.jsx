import {
  Decal,
  Float,
  OrbitControls,
  Preload,
  useTexture,
} from "@react-three/drei";
import { Canvas, useFrame } from "@react-three/fiber";
import React, { Suspense, useRef, useState, useEffect } from "react";
import { usePerf } from "../../context/PerfContext";

// Safe Ball component with error handling
const SafeBall = ({ imgUrl, position = [0, 0, 0], ...props }) => {
  const meshRef = useRef();

  useFrame((state, delta) => {
    if (meshRef.current) {
      meshRef.current.rotation.y += delta * 0.5;
    }
  });

  return (
    <Float speed={1.75} rotationIntensity={1} floatIntensity={2} position={position}>
      <mesh ref={meshRef} castShadow receiveShadow scale={2.75} {...props}>
        <icosahedronGeometry args={[1, 1]} />
        <meshStandardMaterial
          color="#915eff"
          polygonOffset
          polygonOffsetFactor={-5}
          flatShading
        />
      </mesh>
    </Float>
  );
};

const Ball = ({ imgUrl, position = [0, 0, 0], ...props }) => {
  const meshRef = useRef();

  // Use the URL directly since we're now using local files
  const [decal] = useTexture([imgUrl]);

  useFrame((state, delta) => {
    if (meshRef.current) {
      meshRef.current.rotation.y += delta * 0.5;
    }
  });

  return (
    <Float speed={1.75} rotationIntensity={1} floatIntensity={2} position={position}>
      <mesh ref={meshRef} castShadow receiveShadow scale={2.75} {...props}>
        <icosahedronGeometry args={[1, 1]} />
        <meshStandardMaterial
          color="#fff8eb"
          polygonOffset
          polygonOffsetFactor={-5}
          flatShading
        />
        <Decal
          position={[0, 0, 1]}
          rotation={[2 * Math.PI, 0, 6.25]}
          flatShading
          map={decal}
        />
      </mesh>
    </Float>
  );
};

// Shared canvas for multiple balls - reduces WebGL contexts from N to 1
const SharedBallCanvas = ({ icons = [] }) => {
  const containerRef = useRef(null);
  const [inView, setInView] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const { energySaver, incrementWebGLContext, decrementWebGLContext } = usePerf();
  const [ctxReset, setCtxReset] = useState(0);

  // Debug logging (can be removed in production)
  useEffect(() => {
    console.log('SharedBallCanvas loaded with', icons.length, 'technology icons');
  }, [icons]);

  useEffect(() => {
    const mediaQuery = window.matchMedia("(max-width:640px)");
    setIsMobile(mediaQuery.matches);
    const handleMediaQueryChange = (event) => setIsMobile(event.matches);
    mediaQuery.addEventListener("change", handleMediaQueryChange);

    const observer = new IntersectionObserver(
      ([entry]) => setInView(entry.isIntersecting),
      { threshold: 0.1, rootMargin: "50px" }
    );
    if (containerRef.current) observer.observe(containerRef.current);

    const handleVisibility = () => setInView(!document.hidden);
    document.addEventListener("visibilitychange", handleVisibility);

    return () => {
      mediaQuery.removeEventListener("change", handleMediaQueryChange);
      observer.disconnect();
      document.removeEventListener("visibilitychange", handleVisibility);
    };
  }, []);

  // Early return for empty icons
  if (!icons || icons.length === 0) {
    return (
      <div ref={containerRef} className="w-full h-full flex items-center justify-center">
        <div className="text-white text-center">
          <div className="text-4xl mb-2">⚙️</div>
          <div className="text-sm">Loading technologies...</div>
        </div>
      </div>
    );
  }

  if (!inView && !energySaver) return <div ref={containerRef} className="w-full h-full" />;

  return (
    <div ref={containerRef} className="w-full h-full">
      <Canvas
        key={ctxReset}
        frameloop={energySaver ? "never" : inView ? "demand" : "never"}
        dpr={[1, energySaver || isMobile ? 1 : 1.2]}
        camera={{ position: [0, 0, 5], fov: 75 }}
        gl={{
          preserveDrawingBuffer: true,
          powerPreference: "default",
          antialias: !isMobile,
          alpha: true
        }}
        onCreated={({ gl }) => {
          incrementWebGLContext();
          const canvas = gl.domElement;
          const handleLost = (e) => {
            e.preventDefault();
            console.warn('WebGL context lost in SharedBallCanvas');
          };
          const handleRestored = () => {
            console.log('WebGL context restored in SharedBallCanvas');
            setCtxReset(v => v + 1);
          };
          canvas.addEventListener('webglcontextlost', handleLost, false);
          canvas.addEventListener('webglcontextrestored', handleRestored, false);

          return () => {
            decrementWebGLContext();
            canvas.removeEventListener('webglcontextlost', handleLost, false);
            canvas.removeEventListener('webglcontextrestored', handleRestored, false);
          };
        }}
      >
        <ambientLight intensity={0.25} />
        <directionalLight position={[0, 0, 0.05]} />
        <Suspense fallback={null}>
          <OrbitControls enableZoom={false} enableRotate={!energySaver} />
          {icons.map((icon, index) => {
            const angle = (index / icons.length) * Math.PI * 2;
            const radius = icons.length > 4 ? 3 : 2;
            const x = Math.cos(angle) * radius;
            const z = Math.sin(angle) * radius;

            // console.log(`Rendering Ball ${index}: ${icon} at position [${x}, 0, ${z}]`);

            return (
              <Suspense key={`ball-suspense-${index}`} fallback={<SafeBall position={[x, 0, z]} />}>
                <Ball
                  key={`${icon}-${index}`}
                  imgUrl={icon}
                  position={[x, 0, z]}
                />
              </Suspense>
            );
          })}
        </Suspense>
        {!energySaver && <Preload all />}
      </Canvas>
    </div>
  );
};

// Individual ball canvas for single use cases
const BallCanvas = ({ icon }) => {
  const containerRef = useRef(null);
  const [inView, setInView] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const { energySaver } = usePerf();
  const [ctxReset, setCtxReset] = useState(0);

  useEffect(() => {
    const mediaQuery = window.matchMedia("(max-width:640px)");
    setIsMobile(mediaQuery.matches);
    const handleMediaQueryChange = (event) => setIsMobile(event.matches);
    mediaQuery.addEventListener("change", handleMediaQueryChange);

    const observer = new IntersectionObserver(
      ([entry]) => setInView(entry.isIntersecting),
      { threshold: 0.1, rootMargin: "50px" }
    );
    if (containerRef.current) observer.observe(containerRef.current);

    return () => {
      mediaQuery.removeEventListener("change", handleMediaQueryChange);
      observer.disconnect();
    };
  }, []);

  if (!inView && !energySaver) return <div ref={containerRef} className="w-full h-full" />;

  return (
    <div ref={containerRef} className="w-full h-full">
      <Canvas
        key={ctxReset}
        frameloop={energySaver ? "never" : "demand"}
        dpr={[1, energySaver || isMobile ? 1 : 1.2]}
        gl={{
          preserveDrawingBuffer: true,
          powerPreference: "default",
          antialias: !isMobile
        }}
        onCreated={({ gl }) => {
          const canvas = gl.domElement;
          const handleLost = (e) => {
            e.preventDefault();
            console.warn('WebGL context lost in BallCanvas');
          };
          const handleRestored = () => {
            console.log('WebGL context restored in BallCanvas');
            setCtxReset(v => v + 1);
          };
          canvas.addEventListener('webglcontextlost', handleLost, false);
          canvas.addEventListener('webglcontextrestored', handleRestored, false);
        }}
      >
        <ambientLight intensity={0.25} />
        <directionalLight position={[0, 0, 0.05]} />
        <Suspense fallback={null}>
          <OrbitControls enableZoom={false} enableRotate={!energySaver} />
          <Ball imgUrl={icon} />
        </Suspense>
        {!energySaver && <Preload all />}
      </Canvas>
    </div>
  );
};

export default BallCanvas;
export { SharedBallCanvas };
