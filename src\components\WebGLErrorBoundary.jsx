import React from 'react';
import { usePerf } from '../context/PerfContext';

class WebGLErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      errorType: null,
      errorMessage: '',
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error) {
    // Determine error type
    let errorType = 'general';
    let errorMessage = error.message || 'Unknown WebGL error';

    if (error.message?.includes('WebGL')) {
      errorType = 'webgl';
      errorMessage = 'WebGL is not supported or has encountered an error';
    } else if (error.message?.includes('context')) {
      errorType = 'context';
      errorMessage = 'WebGL context was lost or could not be created';
    } else if (error.message?.includes('memory') || error.message?.includes('out of memory')) {
      errorType = 'memory';
      errorMessage = 'Insufficient GPU memory available';
    } else if (error.message?.includes('shader')) {
      errorType = 'shader';
      errorMessage = 'Shader compilation failed';
    }

    return { 
      hasError: true, 
      errorType,
      errorMessage
    };
  }

  componentDidCatch(error, errorInfo) {
    console.error('WebGL Error Boundary caught an error:', error, errorInfo);
    
    // Report to analytics or error tracking service
    if (window.gtag) {
      window.gtag('event', 'webgl_error', {
        error_type: this.state.errorType,
        error_message: this.state.errorMessage,
        component_stack: errorInfo.componentStack
      });
    }
  }

  handleRetry = () => {
    if (this.state.retryCount < 3) {
      this.setState(prevState => ({
        hasError: false,
        errorType: null,
        errorMessage: '',
        retryCount: prevState.retryCount + 1
      }));
    }
  };

  render() {
    if (this.state.hasError) {
      const { fallback, showRetry = true, className = "" } = this.props;
      
      // Use custom fallback if provided
      if (fallback) {
        return typeof fallback === 'function' 
          ? fallback(this.state.errorType, this.handleRetry, this.state.retryCount < 3)
          : fallback;
      }

      // Default fallback UI
      return (
        <div className={`flex flex-col items-center justify-center p-8 bg-gray-900/50 rounded-lg border border-gray-700 ${className}`}>
          <div className="text-center">
            <div className="mb-4">
              <svg className="w-16 h-16 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            
            <h3 className="text-lg font-semibold text-white mb-2">
              {this.getErrorTitle()}
            </h3>
            
            <p className="text-gray-300 mb-4 max-w-md">
              {this.state.errorMessage}
            </p>
            
            <div className="space-y-2">
              {showRetry && this.state.retryCount < 3 && (
                <button
                  onClick={this.handleRetry}
                  className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  Try Again ({3 - this.state.retryCount} attempts left)
                </button>
              )}
              
              <div className="text-sm text-gray-400">
                {this.getErrorSuggestion()}
              </div>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }

  getErrorTitle() {
    switch (this.state.errorType) {
      case 'webgl':
        return '3D Graphics Not Available';
      case 'context':
        return 'Graphics Context Lost';
      case 'memory':
        return 'Insufficient Graphics Memory';
      case 'shader':
        return 'Graphics Rendering Error';
      default:
        return 'Graphics Error';
    }
  }

  getErrorSuggestion() {
    switch (this.state.errorType) {
      case 'webgl':
        return 'Please update your browser or enable hardware acceleration';
      case 'context':
        return 'Try refreshing the page or closing other browser tabs';
      case 'memory':
        return 'Close other applications or browser tabs to free up memory';
      case 'shader':
        return 'Your graphics driver may need updating';
      default:
        return 'Try refreshing the page or contact support if the issue persists';
    }
  }
}

// HOC for easy wrapping of canvas components
export const withWebGLErrorBoundary = (Component, errorBoundaryProps = {}) => {
  return function WrappedComponent(props) {
    return (
      <WebGLErrorBoundary {...errorBoundaryProps}>
        <Component {...props} />
      </WebGLErrorBoundary>
    );
  };
};

// Hook for checking WebGL support
export const useWebGLSupport = () => {
  const [isSupported, setIsSupported] = React.useState(null);
  const [contextType, setContextType] = React.useState(null);

  React.useEffect(() => {
    const canvas = document.createElement('canvas');
    
    try {
      const gl = canvas.getContext('webgl2') || canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
      
      if (gl) {
        setIsSupported(true);
        setContextType(gl instanceof WebGL2RenderingContext ? 'webgl2' : 'webgl');
        
        // Check for common extensions
        const extensions = {
          anisotropic: gl.getExtension('EXT_texture_filter_anisotropic'),
          depthTexture: gl.getExtension('WEBGL_depth_texture'),
          floatTexture: gl.getExtension('OES_texture_float')
        };
        
        console.log('WebGL Support:', {
          version: contextType,
          renderer: gl.getParameter(gl.RENDERER),
          vendor: gl.getParameter(gl.VENDOR),
          extensions
        });
      } else {
        setIsSupported(false);
      }
    } catch (error) {
      console.error('WebGL support check failed:', error);
      setIsSupported(false);
    }
  }, []);

  return { isSupported, contextType };
};

export default WebGLErrorBoundary;
