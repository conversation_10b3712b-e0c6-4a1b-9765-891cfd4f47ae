
import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import App from "./App.jsx";
import { BrowserRouter } from "react-router-dom";
import { PerfProvider } from "./context/PerfContext";

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <PerfProvider>
      <BrowserRouter>
        <App />
      </BrowserRouter>
    </PerfProvider>
  </StrictMode>
);
