import EarthCanvas from "./Earth";
import BallCanvas, { SharedBallCanvas } from "./Ball";
import ComputersCanvas from "./Computers";
import StarsCanvas from "./Stars";
import WebGLErrorBoundary, { withWebGLErrorBoundary } from "../WebGLErrorBoundary";

// Wrap canvas components with error boundaries
const SafeEarthCanvas = withWebGLErrorBoundary(EarthCanvas, {
  fallback: (errorType, retry, canRetry) => (
    <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-900/20 to-green-900/20 rounded-lg">
      <div className="text-center text-white">
        <div className="text-lg mb-2">🌍</div>
        <div className="text-sm">Earth model unavailable</div>
        {canRetry && (
          <button onClick={retry} className="mt-2 px-3 py-1 bg-blue-600 rounded text-xs">
            Retry
          </button>
        )}
      </div>
    </div>
  )
});

const SafeComputersCanvas = withWebGLErrorBoundary(ComputersCanvas, {
  fallback: (errorType, retry, canRetry) => (
    <div className="w-full h-full flex items-center justify-center">
      <div className="text-center text-white">
        <div className="text-6xl mb-4">💻</div>
        <div className="text-sm">3D model unavailable</div>
        {canRetry && (
          <button onClick={retry} className="mt-2 px-3 py-1 bg-blue-600 rounded text-xs">
            Retry
          </button>
        )}
      </div>
    </div>
  )
});

const SafeStarsCanvas = withWebGLErrorBoundary(StarsCanvas, {
  fallback: () => (
    <div className="w-full h-full bg-gradient-to-b from-purple-900/20 to-transparent">
      {/* CSS-only star field fallback */}
      <div className="stars-fallback"></div>
    </div>
  )
});

const SafeSharedBallCanvas = withWebGLErrorBoundary(SharedBallCanvas, {
  fallback: (errorType, retry, canRetry) => (
    <div className="w-full h-full flex items-center justify-center">
      <div className="text-center text-white">
        <div className="text-4xl mb-2">⚙️</div>
        <div className="text-sm">Technology showcase unavailable</div>
        {canRetry && (
          <button onClick={retry} className="mt-2 px-3 py-1 bg-blue-600 rounded text-xs">
            Retry
          </button>
        )}
      </div>
    </div>
  )
});

export {
  SafeEarthCanvas as EarthCanvas,
  BallCanvas,
  SafeSharedBallCanvas as SharedBallCanvas,
  SafeComputersCanvas as ComputersCanvas,
  SafeStarsCanvas as StarsCanvas,
  WebGLErrorBoundary
};
