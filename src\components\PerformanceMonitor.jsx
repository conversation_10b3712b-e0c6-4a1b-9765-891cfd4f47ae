import React, { useState, useEffect } from 'react';
import { usePerf } from '../context/PerfContext';

const PerformanceMonitor = ({ showDetails = false, className = "" }) => {
  const { 
    energySaver, 
    setEnergySaver, 
    memoryUsage, 
    performanceLevel, 
    webglContextCount 
  } = usePerf();
  
  const [isVisible, setIsVisible] = useState(false);
  const [fps, setFps] = useState(0);
  const [frameCount, setFrameCount] = useState(0);
  const [lastTime, setLastTime] = useState(performance.now());

  // FPS monitoring
  useEffect(() => {
    let animationId;
    
    const measureFPS = (currentTime) => {
      setFrameCount(prev => prev + 1);
      
      if (currentTime - lastTime >= 1000) {
        setFps(frameCount);
        setFrameCount(0);
        setLastTime(currentTime);
      }
      
      animationId = requestAnimationFrame(measureFPS);
    };
    
    if (isVisible) {
      animationId = requestAnimationFrame(measureFPS);
    }
    
    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [isVisible, frameCount, lastTime]);

  const getPerformanceColor = () => {
    switch (performanceLevel) {
      case 'high': return 'text-green-400';
      case 'medium': return 'text-yellow-400';
      case 'low': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getMemoryColor = () => {
    if (memoryUsage > 0.8) return 'text-red-400';
    if (memoryUsage > 0.6) return 'text-yellow-400';
    return 'text-green-400';
  };

  const formatMemoryUsage = () => {
    if (performance.memory) {
      const { usedJSHeapSize, totalJSHeapSize } = performance.memory;
      const usedMB = Math.round(usedJSHeapSize / 1024 / 1024);
      const totalMB = Math.round(totalJSHeapSize / 1024 / 1024);
      return `${usedMB}MB / ${totalMB}MB`;
    }
    return 'N/A';
  };

  if (!showDetails && !isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className={`fixed bottom-4 right-4 z-50 bg-gray-800/80 hover:bg-gray-700/80 text-white p-2 rounded-lg transition-colors ${className}`}
        title="Show Performance Monitor"
      >
        📊
      </button>
    );
  }

  return (
    <div className={`fixed bottom-4 right-4 z-50 bg-gray-900/95 backdrop-blur-sm text-white p-4 rounded-lg border border-gray-700 min-w-64 ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <h3 className="text-sm font-semibold">Performance Monitor</h3>
        {!showDetails && (
          <button
            onClick={() => setIsVisible(false)}
            className="text-gray-400 hover:text-white transition-colors"
          >
            ✕
          </button>
        )}
      </div>
      
      <div className="space-y-2 text-xs">
        {/* Performance Level */}
        <div className="flex justify-between">
          <span>Performance:</span>
          <span className={`font-medium ${getPerformanceColor()}`}>
            {performanceLevel.toUpperCase()}
          </span>
        </div>
        
        {/* Energy Saver Status */}
        <div className="flex justify-between">
          <span>Energy Saver:</span>
          <span className={energySaver ? 'text-yellow-400' : 'text-green-400'}>
            {energySaver ? 'ON' : 'OFF'}
          </span>
        </div>
        
        {/* FPS */}
        <div className="flex justify-between">
          <span>FPS:</span>
          <span className={fps < 30 ? 'text-red-400' : fps < 50 ? 'text-yellow-400' : 'text-green-400'}>
            {fps}
          </span>
        </div>
        
        {/* WebGL Contexts */}
        <div className="flex justify-between">
          <span>WebGL Contexts:</span>
          <span className={webglContextCount > 5 ? 'text-red-400' : webglContextCount > 3 ? 'text-yellow-400' : 'text-green-400'}>
            {webglContextCount}
          </span>
        </div>
        
        {/* Memory Usage */}
        <div className="flex justify-between">
          <span>Memory:</span>
          <span className={getMemoryColor()}>
            {Math.round(memoryUsage * 100)}%
          </span>
        </div>
        
        {/* Detailed Memory Info */}
        <div className="flex justify-between text-gray-400">
          <span>Heap:</span>
          <span>{formatMemoryUsage()}</span>
        </div>
        
        {/* Controls */}
        <div className="pt-2 border-t border-gray-700">
          <button
            onClick={() => setEnergySaver(!energySaver)}
            className={`w-full py-1 px-2 rounded text-xs transition-colors ${
              energySaver 
                ? 'bg-yellow-600 hover:bg-yellow-700' 
                : 'bg-green-600 hover:bg-green-700'
            }`}
          >
            {energySaver ? 'Disable Energy Saver' : 'Enable Energy Saver'}
          </button>
        </div>
        
        {/* Warnings */}
        {(memoryUsage > 0.8 || webglContextCount > 6) && (
          <div className="pt-2 border-t border-gray-700">
            <div className="text-red-400 text-xs">
              ⚠️ High resource usage detected
            </div>
            {memoryUsage > 0.8 && (
              <div className="text-xs text-gray-400">
                Consider closing other tabs
              </div>
            )}
            {webglContextCount > 6 && (
              <div className="text-xs text-gray-400">
                Too many 3D contexts active
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default PerformanceMonitor;
