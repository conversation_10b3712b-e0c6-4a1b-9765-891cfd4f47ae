
import React, { Suspense, lazy } from "react";
import { Routes, Route } from "react-router-dom";
import PerformanceMonitor from "./components/PerformanceMonitor";

const Home = lazy(() => import("./Home"));
const AllTechnologies = lazy(() => import("./components/AllTechnologies"));

const App = () => {
  return (
    <>
      <Suspense fallback={<div className="text-white text-center py-10">Loading...</div>}>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/all-technologies" element={<AllTechnologies />} />
        </Routes>
      </Suspense>

      {/* Performance Monitor - only show in development */}
      {process.env.NODE_ENV === 'development' && <PerformanceMonitor />}
    </>
  );
};

export default App;
