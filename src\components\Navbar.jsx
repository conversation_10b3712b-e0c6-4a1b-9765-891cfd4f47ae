
import React, { useState } from "react";
import { Link } from "react-router-dom";
import { styles } from "../styles";
import { navLinks } from "../constants";
import { logo, menu, close } from "../assets";
import { usePerf } from "../context/PerfContext";

const Navbar = () => {
  const [active, setActive] = useState("");
  const [toggle, setToggle] = useState(false);
  const { energySaver, setEnergySaver } = usePerf();

  return (
    <nav className={`${styles.paddingX} w-full flex items-center py-2 z-20
      bg-[#050a208e] backdrop-blur-md border-b border-white/10 fixed`}>
      <div className="w-full flex justify-between items-center max-w-7xl mx-auto">
        <Link
          to="/"
          className="flex items-center gap-0"
          onClick={() => {
            setActive("");
            window.scrollTo(0, 0);
          }}
        >
          <img
            src={`${logo}${logo.includes("?") ? "&" : "?"}tr=f-auto`}
            alt="logo"
            loading="lazy"
            className="w-16 object-contain "
            width={64}
            height={64}
          />
          <p className="text-white text-[17px] font-semibold cursor-pointer">
            <span className="sm:block  font-bold">Siva Bhanu</span> | ML Engineer
          </p>
        </Link>

        <ul className="list-none hidden text-nowrap sm:flex flex-row gap-6 items-center">
          {navLinks.map((link) => (
            <li
              key={link.id}
              className={`${
                active === link.title ? "text-white" : "text-[#ffffff69]"
              } hover:text-[#ffffffac] text-[18px] font-medium cursor-pointer`}
              onClick={() => setActive(link.title)}
            >
              <a href={`#${link.id}`}>{link.title}</a>
            </li>
          ))}
          {/* Energy saver toggle (desktop) */}
          <li className="flex items-center gap-2 text-sm text-[#ffffffb0]">
            <span>Energy saver</span>
            <button onClick={() => setEnergySaver(!energySaver)} className={`w-10 h-6 rounded-full p-1 ${energySaver ? "bg-[#22c55e]" : "bg-[#334155]"}`}>
              <span className={`block w-4 h-4 bg-white rounded-full ${energySaver ? "translate-x-4" : ""}`} />
            </button>
          </li>
        </ul>

        {/* Mobile menu icon */}
        <div className="sm:hidden flex flex-1 justify-end items-center">
          <img
            src={`${(toggle ? close : menu)}${(toggle ? close : menu).includes("?") ? "&" : "?"}tr=f-auto`}
            alt="menu"
            loading="lazy"
            className="w-[28px] object-contain cursor-pointer"
            onClick={() => setToggle(!toggle)}
            width={28}
            height={28}
          />
        </div>
      </div>

      <div
        className={`fixed top-0 right-0 h-full w-3/4 sm:w-1/2 bg-[#050816] shadow-lg transition-transform duration-300 z-40 ${
          toggle ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div className="flex flex-col gap-8 p-6">
          <div className="w-full flex items-center justify-end">
            <img
              src={`${close}${close.includes("?") ? "&" : "?"}tr=f-auto`}
              alt="close"
              loading="lazy"
              className="w-[24px] h-[24px] object-contain cursor-pointer"
              onClick={() => setToggle(false)}
              width={24}
              height={24}
            />
          </div>

          {/* Energy saver toggle (mobile) */}
          <div className="flex items-center justify-between text-[#ffffffb0]">
            <span className="text-[18px]">Energy saver</span>
            <button
              aria-label="Toggle energy saver"
              onClick={() => setEnergySaver(!energySaver)}
              className={`w-12 h-7 rounded-full p-1 transition-colors ${
                energySaver ? "bg-[#22c55e]" : "bg-[#334155]"
              }`}
            >
              <span
                className={`block w-5 h-5 bg-white rounded-full transform transition-transform ${
                  energySaver ? "translate-x-5" : "translate-x-0"
                }`}
              />
            </button>
          </div>

          {navLinks.map((link) => (
            <p
              key={link.id}
              onClick={() => {
                setActive(link.title);
                setToggle(false);
              }}
              className="text-[22px] mt-2 text-[#ffffffb0] hover:text-white font-medium cursor-pointer transition-all"
            >
              <a href={`#${link.id}`}>{link.title}</a>
            </p>
          ))}
        </div>
      </div>

      {toggle && (
        <div
          onClick={() => setToggle(false)}
          className="fixed inset-0 bg-black/30 backdrop-blur-sm z-30"
        ></div>
      )}
    </nav>
  );
};

export default Navbar;
