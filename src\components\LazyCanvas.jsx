import React, { useState, useRef, useEffect } from 'react';
import { usePerf } from '../context/PerfContext';

const LazyCanvas = ({ 
  children, 
  threshold = 0.1, 
  rootMargin = "100px",
  fallback = null,
  className = "",
  style = {},
  ...props 
}) => {
  const [shouldRender, setShouldRender] = useState(false);
  const [hasRendered, setHasRendered] = useState(false);
  const containerRef = useRef(null);
  const { energySaver, performanceLevel } = usePerf();

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        const isIntersecting = entry.isIntersecting;
        setShouldRender(isIntersecting);
        
        if (isIntersecting && !hasRendered) {
          setHasRendered(true);
        }
        
        // Log for debugging
        console.log(`LazyCanvas intersection: ${isIntersecting}`);
      },
      { 
        threshold: performanceLevel === 'low' ? 0.3 : threshold,
        rootMargin: performanceLevel === 'low' ? "50px" : rootMargin
      }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    // Handle visibility changes
    const handleVisibilityChange = () => {
      if (document.hidden) {
        setShouldRender(false);
      } else if (hasRendered) {
        // Only re-render if it was previously rendered and is in view
        const rect = containerRef.current?.getBoundingClientRect();
        if (rect && rect.top < window.innerHeight && rect.bottom > 0) {
          setShouldRender(true);
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      observer.disconnect();
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [threshold, rootMargin, hasRendered, performanceLevel]);

  // Don't render if energy saver is on and not in view
  const shouldShow = shouldRender && (!energySaver || hasRendered);

  return (
    <div 
      ref={containerRef} 
      className={className}
      style={style}
      {...props}
    >
      {shouldShow ? children : (fallback || <div className="w-full h-full bg-gray-900/20 animate-pulse" />)}
    </div>
  );
};

export default LazyCanvas;
