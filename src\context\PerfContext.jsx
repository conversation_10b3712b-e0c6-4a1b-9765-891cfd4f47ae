import React, { createContext, useContext, useMemo, useState, useEffect, useCallback } from "react";

const PerfContext = createContext({
  energySaver: false,
  setEnergySaver: () => {},
  memoryUsage: 0,
  performanceLevel: 'high',
  webglContextCount: 0,
  incrementWebGLContext: () => {},
  decrementWebGLContext: () => {}
});

export const PerfProvider = ({ children }) => {
  const [energySaver, setEnergySaver] = useState(false);
  const [memoryUsage, setMemoryUsage] = useState(0);
  const [performanceLevel, setPerformanceLevel] = useState('high');
  const [webglContextCount, setWebglContextCount] = useState(0);

  // Monitor memory usage and adjust performance automatically
  useEffect(() => {
    const checkPerformance = () => {
      // Check memory usage if available
      if (performance.memory) {
        const { usedJSHeapSize, totalJSHeapSize } = performance.memory;
        const usage = usedJSHeapSize / totalJSHeapSize;
        setMemoryUsage(usage);

        // Auto-adjust performance based on memory usage
        if (usage > 0.85) {
          setPerformanceLevel('low');
          setEnergySaver(true);
          console.warn('High memory usage detected - enabling energy saver mode');
        } else if (usage > 0.7) {
          setPerformanceLevel('medium');
        } else {
          setPerformanceLevel('high');
        }
      }

      // Check WebGL context count
      if (webglContextCount > 6) {
        console.warn(`High WebGL context count: ${webglContextCount} - consider enabling energy saver`);
        setEnergySaver(true);
      }

      // Check if page is hidden (tab backgrounded)
      if (document.hidden) {
        setEnergySaver(true);
      }
    };

    // Check performance every 10 seconds
    const interval = setInterval(checkPerformance, 10000);

    // Check immediately
    checkPerformance();

    // Listen for visibility changes
    const handleVisibilityChange = () => {
      if (document.hidden) {
        setEnergySaver(true);
      } else {
        // Re-enable after a delay when tab becomes visible
        setTimeout(() => {
          if (memoryUsage < 0.7) {
            setEnergySaver(false);
          }
        }, 1000);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      clearInterval(interval);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [webglContextCount, memoryUsage]);

  const incrementWebGLContext = useCallback(() => {
    setWebglContextCount(prev => {
      const newCount = prev + 1;
      console.log(`WebGL contexts: ${newCount}`);
      return newCount;
    });
  }, []);

  const decrementWebGLContext = useCallback(() => {
    setWebglContextCount(prev => {
      const newCount = Math.max(0, prev - 1);
      console.log(`WebGL contexts: ${newCount}`);
      return newCount;
    });
  }, []);

  const value = useMemo(() => ({
    energySaver,
    setEnergySaver,
    memoryUsage,
    performanceLevel,
    webglContextCount,
    incrementWebGLContext,
    decrementWebGLContext
  }), [energySaver, memoryUsage, performanceLevel, webglContextCount, incrementWebGLContext, decrementWebGLContext]);

  return <PerfContext.Provider value={value}>{children}</PerfContext.Provider>;
};

export const usePerf = () => useContext(PerfContext);

