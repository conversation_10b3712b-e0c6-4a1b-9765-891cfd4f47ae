
import React, { Suspense, useEffect, useState, useRef } from "react";
import { OrbitControls, Preload, useGLTF } from "@react-three/drei";
import { Canvas } from "@react-three/fiber";
import CanvasLoader from "../Loader";
import { usePerf } from "../../context/PerfContext";

const Earth = () => {
  const earth = useGLTF('./planet/scene.gltf');

  useEffect(() => {
    earth.scene.traverse((child) => {
      if (child.isMesh) {
        child.geometry?.center();
      }
    });
  }, [earth]);

  return (
    <mesh>
      <ambientLight intensity={0.5} />
      <directionalLight position={[5, 5, 5]} intensity={1} />
      <primitive object={earth.scene} scale={2.5} position-y={0} rotation-y = {0} />
    </mesh>
  );
};

const EarthCanvas = () => {
  const containerRef = useRef(null);
  const [isMobile, setIsMobile] = useState(false);
  const [inView, setInView] = useState(true);
  const { energySaver, incrementWebGLContext, decrementWebGLContext } = usePerf();
  const [ctxReset, setCtxReset] = useState(0);

  useEffect(() => {
    const mediaQuery = window.matchMedia("(max-width:640px)");
    setIsMobile(mediaQuery.matches);
    const handleMediaQueryChange = (event) => setIsMobile(event.matches);
    mediaQuery.addEventListener("change", handleMediaQueryChange);

    const observer = new IntersectionObserver(
      ([entry]) => setInView(entry.isIntersecting),
      { threshold: 0.1, rootMargin: "100px" }
    );
    if (containerRef.current) observer.observe(containerRef.current);

    const handleVisibility = () => setInView(!document.hidden);
    document.addEventListener("visibilitychange", handleVisibility);

    return () => {
      mediaQuery.removeEventListener("change", handleMediaQueryChange);
      observer.disconnect();
      document.removeEventListener("visibilitychange", handleVisibility);
    };
  }, []);

  return (
    <div ref={containerRef}>
      {!(isMobile && !inView) && (
        <Canvas
          key={ctxReset}
          frameloop={energySaver ? "never" : inView ? "always" : "never"}
          dpr={[1, energySaver || isMobile ? 1 : 1.5]}
          gl={{ powerPreference: "default", antialias: true }}
          onCreated={({ gl, scene }) => {
            incrementWebGLContext();
            const canvas = gl.domElement;
            const handleLost = (e) => {
              e.preventDefault();
              console.warn('WebGL context lost in EarthCanvas - cleaning up resources');
              // Clean up resources
              if (scene) {
                scene.traverse((child) => {
                  if (child.material) {
                    child.material.dispose?.();
                  }
                  if (child.geometry) {
                    child.geometry.dispose?.();
                  }
                });
              }
            };
            const handleRestored = () => {
              console.log('WebGL context restored in EarthCanvas - reloading');
              setCtxReset(v => v + 1);
            };
            canvas.addEventListener('webglcontextlost', handleLost, false);
            canvas.addEventListener('webglcontextrestored', handleRestored, false);

            // Return cleanup function
            return () => {
              decrementWebGLContext();
              canvas.removeEventListener('webglcontextlost', handleLost, false);
              canvas.removeEventListener('webglcontextrestored', handleRestored, false);
            };
          }}
        >
          <Suspense fallback={<CanvasLoader />}>
            <OrbitControls autoRotate={!energySaver} enableZoom={false}/>
            <Earth />
          </Suspense>
          {!energySaver && <Preload all />}
        </Canvas>
      )}
    </div>
  );
};

export default EarthCanvas;
