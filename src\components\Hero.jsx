
import { motion } from "framer-motion";
import { ComputersCanvas } from "./canvas";
import { styles } from "../styles";
import LazyCanvas from "./LazyCanvas";


const Hero = () => {
  return (
    <section className="relative w-full   h-screen mx-auto">
      <div
        className={`${styles.paddingX} inset-0 sm:top-[70px] top-[90px] absolute max-w-7xl mx-auto flex flex-row items-start gap-5`}
      >
        <div className="flex flex-col justify-center items-center mt-5">
          <div className="w-5 h-5 rounded-full bg-[#915eff]"></div>
          <div className="w-1 sm:h-80 h-40 violet-gradient"></div>
        </div>
        <div>
          <h1 className={`${styles.heroHeadText} text-white`}>
            Hi, I'm <span className="text-[#915eff]">Siva Bhanu</span>
          </h1>
          <p className={`${styles.heroSubText} mt-2 text-white`}>
            I build machine learning systems, data pipelines, and
            <br className="sm:block hidden" /> analytical applications
          </p>
        </div>
      </div>
      <LazyCanvas
        threshold={0.2}
        rootMargin="200px"
        fallback={<div className="w-full h-full bg-gradient-to-b from-transparent to-gray-900/30" />}
      >
        <ComputersCanvas />
      </LazyCanvas>
      <div className="absolute sm:bottom-24 bottom-36 w-full flex justify-center items-center">
        <a href="#about">
          <div className="w-[35px] h-[64px] rounded-3xl border-4 border-secondary flex justify-center items-start p-2">
            <motion.div
              animate={{ y: [0, 24, 0] }}
              transition={{ duration: 1.5, repeat: Infinity, repeatType: "loop" }}
              className="w-3 h-3 rounded-full bg-secondary mb-1"
            />
          </div>
        </a>
      </div>
    </section>
  );
};

export default Hero;
