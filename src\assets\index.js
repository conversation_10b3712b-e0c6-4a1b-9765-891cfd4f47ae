
const logo =
  "https://ik.imagekit.io/bqzlidc77g/my%20portfolio/Swastik_Yadav_logo.png?updatedAt=1750349700579";
const backend =
  "https://ik.imagekit.io/bqzlidc77g/my%20portfolio/backend.png?updatedAt=1749961127387";
const creator =
  "https://ik.imagekit.io/bqzlidc77g/my%20portfolio/creator.png?updatedAt=1749961125453";
const mobile =
  "https://ik.imagekit.io/bqzlidc77g/my%20portfolio/mobile.png?updatedAt=1749961116254";
const web =
  "https://ik.imagekit.io/bqzlidc77g/my%20portfolio/web.png?updatedAt=1749961126214";
const github =
  "https://ik.imagekit.io/bqzlidc77g/my%20portfolio/github.png?updatedAt=1749965006566";
const menu =
  "https://ik.imagekit.io/bqzlidc77g/my%20portfolio/menu.png?updatedAt=1750673289882";
const close =
  "https://ik.imagekit.io/bqzlidc77g/my%20portfolio/close.png?updatedAt=1750673269591";

const css =
  "https://ik.imagekit.io/bqzlidc77g/my%20portfolio/tech/css.png?updatedAt=1749961104488";
const docker = "/tech/docker.svg";
const figma =
  "https://ik.imagekit.io/bqzlidc77g/my%20portfolio/tech/figma.png?updatedAt=1749961103288";
const git = "/tech/git.svg";
const html =
  "https://ik.imagekit.io/bqzlidc77g/my%20portfolio/tech/html.png?updatedAt=1749961104418";
const javascript =
  "https://ik.imagekit.io/bqzlidc77g/my%20portfolio/tech/javascript.png?updatedAt=1749961104755";
const mongodb =
  "https://ik.imagekit.io/bqzlidc77g/my%20portfolio/tech/mongodb.png?updatedAt=1749961104590";
const nodejs =
  "https://ik.imagekit.io/bqzlidc77g/my%20portfolio/tech/nodejs.png?updatedAt=1749961104709";
const reactjs =
  "https://ik.imagekit.io/bqzlidc77g/my%20portfolio/tech/reactjs.png?updatedAt=1749961105057";
const redux =
  "https://ik.imagekit.io/bqzlidc77g/my%20portfolio/tech/redux.png?updatedAt=1749961111710";
const tailwind =
  "https://ik.imagekit.io/bqzlidc77g/my%20portfolio/tech/tailwind.png?updatedAt=1749961111586";
const typescript =
  "https://ik.imagekit.io/bqzlidc77g/my%20portfolio/tech/typescript.png?updatedAt=1749961112627";
const threejs =
  "https://ik.imagekit.io/bqzlidc77g/my%20portfolio/tech/threejs.svg?updatedAt=1749961112674";

const meta =
  "https://ik.imagekit.io/bqzlidc77g/my%20portfolio/company/meta.png?updatedAt=1749961112288";
const shopify =
  "https://ik.imagekit.io/bqzlidc77g/my%20portfolio/company/shopify.png?updatedAt=1749961112545";

const sheryians =
  "https://ik.imagekit.io/bqzlidc77g/my%20portfolio/company/sheryians.jpg?updatedAt=1750261292267";
const freelance =
  "https://ik.imagekit.io/bqzlidc77g/my%20portfolio/company/images.png?updatedAt=1750261804051";

const macbookpro =
  "https://ik.imagekit.io/bqzlidc77g/my%20portfolio/MacbookPro_TGyn1EY09X.png?updatedAt=1750225807061";
const falverra =
  "https://ik.imagekit.io/bqzlidc77g/my%20portfolio/Screenshot%202025-06-21%20015159.png?updatedAt=1750451051537";
const tripguide =
  "https://ik.imagekit.io/bqzlidc77g/my%20portfolio/tripguide.png?updatedAt=1749961172334";

// Use local SVG files to avoid CORS issues
const python = "/tech/python.svg";
const scikitlearn = "/tech/scikit-learn.svg";
const tensorflow = "/tech/tensorflow.svg";
const flask = "/tech/flask.svg";
const mysql = "/tech/mysql.svg";
const jupyter = "/tech/jupyter.svg";
const streamlit = "/tech/jupyter.svg"; // Reuse jupyter for now

const headshot = logo;

export {
  logo,
  backend,
  creator,
  mobile,
  web,
  github,
  menu,
  close,
  css,
  docker,
  figma,
  git,
  html,
  javascript,
  mongodb,
  nodejs,
  reactjs,
  redux,
  tailwind,
  typescript,
  threejs,
  meta,
  shopify,
  freelance,
  sheryians,
  macbookpro,
  falverra,
  tripguide,
  python,
  scikitlearn,
  tensorflow,
  flask,
  mysql,
  jupyter,
  streamlit,
  headshot,
};
