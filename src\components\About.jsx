
import React from "react";
import Tilt from "react-parallax-tilt";
import { motion } from "framer-motion";
import { styles } from "../styles";
import { services } from "../constants";
import { fadeIn, textVariant } from "../utils/motion";
import { div } from "framer-motion/client";
import { SectionWrapper } from "../hoc";
import { headshot } from "../assets";

const ServiceCard = ({ index, title, icon }) => {
  return (
    <Tilt className="xs:w-[250px] w-full">
      <motion.div
        variants={fadeIn("right", "spring", 0.5 * index, 0.75)}
        className="w-full green-pink-gradient shadow-card rounded-[20px] p-[1px]"
      >
        <div
          options={{
            max: 45,
            scale: 1,
            speed: 450,
          }}
          className="bg-tertiary rounded-[20px] py-5 px-12 min-h-[280px] flex justify-evenly items-center flex-col"
        >
          <img
            src={`${icon}${icon.includes("?") ? "&" : "?"}tr=f-auto`}
            alt={title}
            loading="lazy"
            width={64}
            height={64}
            className="w-16 h-16 object-contain"
          />

          <h3 className="text-white text-[20px] font-bold text-center">
            {title}
          </h3>
        </div>
      </motion.div>
    </Tilt>
  );
};

const About = () => {
  return (
    <>
      <motion.div variants={textVariant()}>
        <p className={styles.sectionSubText}>Introduction</p>
        <h2 className={styles.sectionHeadText}>
          <span className="heading-gradient">About Me</span>
        </h2>
        <div className="mt-6 flex justify-end">
          <img src={`${headshot}${headshot.includes("?") ? "&" : "?"}tr=f-auto`}
               alt="Headshot" className="w-24 h-24 rounded-full ring-2 ring-[#915eff]/40 object-cover" />
        </div>
      </motion.div>
      <motion.p
        variants={fadeIn("", "", 0.1, 1)}
        className="mt-4 text-secondary text-[14px] sm:text-[17px] max-w-3xl sm:leading-[30px] leading-1"
      >
        I’m Siva Bhanu — a Machine Learning Engineer and Data Scientist with
        strong Python expertise. I design end-to-end ML pipelines from data
        ingestion and feature engineering to model training, evaluation, and
        deployment. My toolkit includes scikit-learn, TensorFlow/Keras, NLP,
        SQL/MySQL, Flask/Streamlit, Docker, and real-time visualizations with
        Plotly. I’m currently pursuing a B.Tech in CSE (JNTUA) and I’m
        passionate about building practical AI solutions that create business
        impact.
      </motion.p>
      <div className="mt-20 flex  flex-wrap gap-10">
        {services.map((service, index) => (
          <ServiceCard key={index} index={index} {...service} />
        ))}
      </div>
    </>
  );
};

export default SectionWrapper(About, "about");
