
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  build: {
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      output: {
        manualChunks(id) {
          if (!id.includes('node_modules')) return;
          if (id.includes('react-router') || id.includes('react-dom') || id.includes('react/jsx-runtime'))
            return 'vendor-react';
          if (id.includes('framer-motion') || id.includes('motion-dom') || id.includes('motion-utils'))
            return 'vendor-motion';
          if (id.includes('/three/') && !id.includes('@react-three') && !id.includes('three-stdlib'))
            return 'vendor-three-core';
          if (id.includes('@react-three/')) return 'vendor-r3f';
          if (id.includes('three-stdlib')) return 'vendor-three-stdlib';
          if (id.includes('maath')) return 'vendor-maath';
        },
      },
    },
  },
})
