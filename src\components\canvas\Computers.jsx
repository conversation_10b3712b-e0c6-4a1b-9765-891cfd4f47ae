
import React from "react";
import { OrbitControls, Preload, useGLTF, Float } from "@react-three/drei";
import { Suspense, useEffect, useState, useRef } from "react";
import { Canvas } from "@react-three/fiber";
import CanvasLoader from "../Loader";
import { usePerf } from "../../context/PerfContext";

const Computers = ({ isMobile }) => {
  const computer = useGLTF("/desktop_pc/scene.gltf");

  useEffect(() => {
    computer.scene.traverse((child) => {
      if (child.isMesh) {
        child.geometry?.center();
      }
    });
  }, [computer]);

  return (
    <mesh>
      <hemisphereLight intensity={isMobile ? 1.2 : 2} />
      <pointLight intensity={isMobile ? 2 : 3} />
      <spotLight intensity={isMobile ? 0.8 : 1} castShadow={!isMobile} />
      <Float speed={isMobile ? 1.4 : 2} rotationIntensity={isMobile ? 0.15 : 0.2}
             floatIntensity={isMobile ? 1.0 : 1.5}>
        <primitive
          object={computer.scene}
          scale={isMobile ? 0.5 : 0.75}
          position={isMobile ? [0, -1.6, -1.2] : [0, -2.6, -1.5]}
          rotation={isMobile ? [0, 0, -0.05] : [-0.01, -0.2, -0.05]}
        />
      </Float>
    </mesh>
  );
};

const ComputersCanvas = () => {
  const [isMobile, setisMobile] = useState(false);
  const containerRef = useRef(null);
  const [inView, setInView] = useState(true);
  const { energySaver, incrementWebGLContext, decrementWebGLContext } = usePerf();
  const [ctxReset, setCtxReset] = useState(0);

  useEffect(() => {
    const mediaQuery = window.matchMedia("(max-width:640px)");
    setisMobile(mediaQuery.matches);
    const handleMediaQueryChange = (event) => setisMobile(event.matches);
    mediaQuery.addEventListener("change", handleMediaQueryChange);

    const observer = new IntersectionObserver(
      ([entry]) => setInView(entry.isIntersecting),
      { threshold: 0.1, rootMargin: "100px" }
    );
    if (containerRef.current) observer.observe(containerRef.current);

    const handleVisibility = () => setInView(!document.hidden);
    document.addEventListener("visibilitychange", handleVisibility);

    return () => {
      mediaQuery.removeEventListener("change", handleMediaQueryChange);
      observer.disconnect();
      document.removeEventListener("visibilitychange", handleVisibility);
    };
  }, []);

  return (
    <div ref={containerRef}>
      {!(isMobile && !inView) && (
        <Canvas
          key={ctxReset}
          frameloop={energySaver ? "never" : inView ? "always" : "never"}
          dpr={[1, energySaver || isMobile ? 1 : 1.5]}
          gl={{ powerPreference: "default", antialias: true }}
          onCreated={({ gl, scene, camera }) => {
            incrementWebGLContext();
            const canvas = gl.domElement;
            const handleLost = (e) => {
              e.preventDefault();
              console.warn('WebGL context lost in ComputersCanvas - pausing animations');
              // Clear any ongoing animations or intervals
              if (scene) {
                scene.traverse((child) => {
                  if (child.material) {
                    child.material.dispose?.();
                  }
                  if (child.geometry) {
                    child.geometry.dispose?.();
                  }
                });
              }
            };
            const handleRestored = () => {
              console.log('WebGL context restored in ComputersCanvas - reloading resources');
              // Force a remount to fully reset GL state
              setCtxReset((v) => v + 1);
            };
            canvas.addEventListener("webglcontextlost", handleLost, false);
            canvas.addEventListener("webglcontextrestored", handleRestored, false);

            // Store cleanup function for component unmount
            return () => {
              decrementWebGLContext();
              canvas.removeEventListener("webglcontextlost", handleLost, false);
              canvas.removeEventListener("webglcontextrestored", handleRestored, false);
            };
          }}
        >
        <Suspense fallback={<CanvasLoader />}>
          <OrbitControls
            enableZoom={false}
            enableRotate={true}
            maxPolarAngle={Math.PI / 2}
            minPolarAngle={Math.PI / 2}
          />
          <Computers isMobile={isMobile} />
        </Suspense>
        {!energySaver && <Preload all />}
      </Canvas>
      )}
    </div>
  );
};

export default ComputersCanvas;
