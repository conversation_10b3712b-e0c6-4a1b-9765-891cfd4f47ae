
import React from "react";
import { useRef, useState, Suspense, useEffect } from "react";
import { usePerf } from "../../context/PerfContext";
import { PointMaterial, Preload, Points } from "@react-three/drei";
import { Canvas, useFrame } from "@react-three/fiber";
import * as random from "maath/random/dist/maath-random.esm";

const Stars = ({ count = 5000, ...props }) => {
  const ref = useRef();
  const [sphere] = useState(() => random.inSphere(new Float32Array(count), { radius: 1.2 }));
  const animationRef = useRef(true);

  useFrame((state, delta) => {
    if (ref.current && animationRef.current) {
      ref.current.rotation.x -= delta / 10;
      ref.current.rotation.y -= delta / 15;
    }
  });

  useEffect(() => {
    animationRef.current = true;
    return () => {
      animationRef.current = false;
    };
  }, []);

  return (
    <group rotation={[0, 0, Math.PI / 4]}>
      <Points ref={ref} positions={sphere} stride={3} frustumCulled {...props}>
        <PointMaterial
          transparent
          color="#f272c8"
          size={0.002}
          sizeAttenuation={true}
          depthWrite={false}
        />
      </Points>
    </group>
  );
};

const StarsCanvas = () => {
  const containerRef = useRef(null);
  const [inView, setInView] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const { energySaver, incrementWebGLContext, decrementWebGLContext } = usePerf();
  const [ctxReset, setCtxReset] = useState(0);

  useEffect(() => {
    const mediaQuery = window.matchMedia("(max-width:640px)");
    setIsMobile(mediaQuery.matches);
    const handleMediaQueryChange = (event) => setIsMobile(event.matches);
    mediaQuery.addEventListener("change", handleMediaQueryChange);

    const observer = new IntersectionObserver(
      ([entry]) => setInView(entry.isIntersecting),
      { threshold: 0.1, rootMargin: "100px" }
    );
    if (containerRef.current) observer.observe(containerRef.current);

    const handleVisibility = () => setInView(!document.hidden);
    document.addEventListener("visibilitychange", handleVisibility);

    return () => {
      mediaQuery.removeEventListener("change", handleMediaQueryChange);
      observer.disconnect();
      document.removeEventListener("visibilitychange", handleVisibility);
    };
  }, []);

  return (
    <div ref={containerRef} className="w-full h-full absolute inset-0 z-[-1] pointer-events-none">
      {!(isMobile && !inView) && (
        <Canvas
          key={ctxReset}
          frameloop={energySaver ? "never" : inView ? "always" : "never"}
          dpr={[1, energySaver || isMobile ? 1 : 1.5]}
          gl={{ powerPreference: "default", antialias: true }}
          onCreated={({ gl, scene }) => {
            incrementWebGLContext();
            const canvas = gl.domElement;
            const handleLost = (e) => {
              e.preventDefault();
              console.warn('WebGL context lost in StarsCanvas - pausing animations');
              // Clean up resources
              if (scene) {
                scene.traverse((child) => {
                  if (child.material) {
                    child.material.dispose?.();
                  }
                  if (child.geometry) {
                    child.geometry.dispose?.();
                  }
                });
              }
            };
            const handleRestored = () => {
              console.log('WebGL context restored in StarsCanvas - reloading');
              setCtxReset(v => v + 1);
            };
            canvas.addEventListener('webglcontextlost', handleLost, false);
            canvas.addEventListener('webglcontextrestored', handleRestored, false);

            // Return cleanup function
            return () => {
              decrementWebGLContext();
              canvas.removeEventListener('webglcontextlost', handleLost, false);
              canvas.removeEventListener('webglcontextrestored', handleRestored, false);
            };
          }}
        >
          <Suspense fallback={null}><Stars count={isMobile ? 1500 : 5000} /></Suspense>
          {!energySaver && <Preload all />}
        </Canvas>
      )}
    </div>
  );
};

export default StarsCanvas;
