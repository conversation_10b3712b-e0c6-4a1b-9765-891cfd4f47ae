
import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import { styles } from "../styles";
import { SharedBallCanvas } from "./canvas";
import { SectionWrapper } from "../hoc";
import { technologies } from "../constants";
import { useNavigate } from "react-router-dom";

const Tech = () => {
  const [isMobile, setIsMobile] = useState(false);
  const navigate = useNavigate();

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    handleResize(); // initial check
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const visibleTechs = isMobile ? technologies.slice(0, 5) : technologies;

  return (
    <>
      <motion.div className="w-full text-center py-10">
        <p className={styles.sectionSubText}>What I Use to Build</p>
        <h2 className={styles.sectionHeadText}>
          <span className="heading-gradient">Technologies</span>
        </h2>
      </motion.div>

      <div className="flex flex-col items-center gap-10">
        {/* Shared Canvas for all technology balls - reduces WebGL contexts from 8 to 1 */}
        <div className="w-full max-w-4xl h-96 relative">
          <SharedBallCanvas icons={visibleTechs.map(tech => tech.icon)} />
        </div>

        {/* Technology labels */}
        <div className="flex flex-row flex-wrap justify-center gap-6">
          {visibleTechs.map((tech) => (
            <div key={tech.name} className="text-center">
              <p className="text-white text-sm font-medium">{tech.name}</p>
            </div>
          ))}
        </div>

        {isMobile && (
          <div
            onClick={() => navigate("/all-technologies")}
            className="w-32 h-12 rounded-full border border-dashed border-[#915eff] flex items-center justify-center cursor-pointer hover:bg-[#915eff]/10 text-[#915eff] text-sm font-medium transition"
          >
            + More
          </div>
        )}
      </div>
    </>
  );
};

export default SectionWrapper(Tech, "skills");
